#!/bin/bash
# DevOps脚本：检测代码仓库中的关键字
# 扫描指定目录下所有Git仓库的所有分支和标签中的文件内容

set -e

# 配置变量
BASE_DIR=""
KEYWORDS=""
OUTPUT_FILE=""
LOG_FILE="scan_$(date +%Y%m%d_%H%M%S).log"
TEMP_DIR="/tmp/repo_scan_$$"
MAX_FILE_SIZE=10485760  # 10MB
PARALLEL_JOBS=4

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log() {
    echo -e "${GREEN}[$(date '+%Y-%m-%d %H:%M:%S')]${NC} $1" | tee -a "$LOG_FILE"
}

log_error() {
    echo -e "${RED}[ERROR $(date '+%Y-%m-%d %H:%M:%S')]${NC} $1" | tee -a "$LOG_FILE"
}

log_warn() {
    echo -e "${YELLOW}[WARN $(date '+%Y-%m-%d %H:%M:%S')]${NC} $1" | tee -a "$LOG_FILE"
}

# 显示帮助
show_help() {
    cat << EOF
用法: $0 [选项]

选项:
    -d, --dir DIR           要扫描的目录路径 (必需)
    -k, --keywords WORDS    要搜索的关键字，用逗号分隔 (必需)
    -o, --output FILE       输出结果文件
    -j, --jobs NUM          并行作业数 (默认: 4)
    -h, --help              显示帮助信息

示例:
    $0 -d /path/to/linux -k "devops.aishu.cn,greed,flora" -o results.txt
    $0 --dir ./repositories --keywords "sensitive_keyword" --jobs 8

EOF
}

# 解析命令行参数
parse_args() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            -d|--dir)
                BASE_DIR="$2"
                shift 2
                ;;
            -k|--keywords)
                KEYWORDS="$2"
                shift 2
                ;;
            -o|--output)
                OUTPUT_FILE="$2"
                shift 2
                ;;
            -j|--jobs)
                PARALLEL_JOBS="$2"
                shift 2
                ;;
            -h|--help)
                show_help
                exit 0
                ;;
            *)
                log_error "未知参数: $1"
                show_help
                exit 1
                ;;
        esac
    done

    # 验证必需参数
    if [[ -z "$BASE_DIR" ]]; then
        log_error "必须指定扫描目录 (-d)"
        show_help
        exit 1
    fi

    if [[ -z "$KEYWORDS" ]]; then
        log_error "必须指定关键字 (-k)"
        show_help
        exit 1
    fi

    if [[ ! -d "$BASE_DIR" ]]; then
        log_error "目录不存在: $BASE_DIR"
        exit 1
    fi

    # 设置默认输出文件
    if [[ -z "$OUTPUT_FILE" ]]; then
        OUTPUT_FILE="scan_results_$(date +%Y%m%d_%H%M%S).txt"
    fi
}

# 创建临时目录
setup_temp_dir() {
    mkdir -p "$TEMP_DIR"
    trap "cleanup" EXIT
}

# 清理函数
cleanup() {
    if [[ -d "$TEMP_DIR" ]]; then
        rm -rf "$TEMP_DIR"
    fi
}

# 查找所有Git仓库
find_git_repos() {
    local base_dir="$1"
    log "正在查找Git仓库..."

    find "$base_dir" -type d -name ".git" 2>/dev/null | while read -r git_dir; do
        repo_dir=$(dirname "$git_dir")
        echo "$repo_dir"
    done | sort | uniq
}

# 获取仓库的所有分支和标签
get_all_refs() {
    local repo_path="$1"
    local refs_file="$2"

    cd "$repo_path" || return 1

    # 获取所有分支
    git branch -a 2>/dev/null | sed 's/^[* ] *//' | sed 's/remotes\/origin\///' | grep -v "^HEAD" | sort | uniq | while read -r branch; do
        if [[ -n "$branch" ]]; then
            echo "branch:$branch" >> "$refs_file"
        fi
    done

    # 获取所有标签
    git tag 2>/dev/null | while read -r tag; do
        if [[ -n "$tag" ]]; then
            echo "tag:$tag" >> "$refs_file"
        fi
    done
}

# 检查文件是否为文本文件
is_text_file() {
    local file="$1"

    # 检查文件大小
    if [[ ! -f "$file" ]]; then
        return 1
    fi

    local size
    size=$(stat -c%s "$file" 2>/dev/null || stat -f%z "$file" 2>/dev/null || echo "0")
    if [[ "$size" -gt "$MAX_FILE_SIZE" ]]; then
        return 1
    fi

    # 检查文件类型
    if file "$file" 2>/dev/null | grep -q -E "(text|empty|ASCII|UTF-8)"; then
        return 0
    fi

    return 1
}

# 在指定分支/标签中搜索关键字
search_in_ref() {
    local repo_path="$1"
    local ref_type="$2"
    local ref_name="$3"
    local keywords="$4"
    local result_file="$5"

    cd "$repo_path" || return 1

    # 保存当前分支
    local current_branch
    current_branch=$(git rev-parse --abbrev-ref HEAD 2>/dev/null || echo "")

    # 切换到指定分支/标签
    if ! git checkout "$ref_name" >/dev/null 2>&1; then
        return 1
    fi

    # 获取所有文件列表
    git ls-files 2>/dev/null | while read -r file; do
        if is_text_file "$file"; then
            # 将关键字转换为grep模式
            local grep_pattern
            grep_pattern=$(echo "$keywords" | tr ',' '|')

            # 搜索关键字（区分大小写）
            grep -n -E "$grep_pattern" "$file" 2>/dev/null | while IFS=: read -r line_num line_content; do
                # 确定匹配的具体关键字
                local matched_keyword=""
                IFS=',' read -ra KEYWORD_ARRAY <<< "$keywords"
                for keyword in "${KEYWORD_ARRAY[@]}"; do
                    if echo "$line_content" | grep -q "$keyword"; then
                        matched_keyword="$keyword"
                        break
                    fi
                done

                # 输出结果
                printf "%s|%s|%s|%s|%s|%s|%s\n" \
                    "$(basename "$repo_path")" \
                    "$ref_type" \
                    "$ref_name" \
                    "$file" \
                    "$line_num" \
                    "$matched_keyword" \
                    "${line_content:0:200}" >> "$result_file"
            done
        fi
    done

    # 恢复原分支
    if [[ -n "$current_branch" && "$current_branch" != "HEAD" ]]; then
        git checkout "$current_branch" >/dev/null 2>&1 || true
    fi
}

# 扫描单个仓库
scan_repository() {
    local repo_path="$1"
    local keywords="$2"
    local repo_result_file="$TEMP_DIR/$(basename "$repo_path")_results.txt"

    log "扫描仓库: $(basename "$repo_path")"

    # 获取所有分支和标签
    local refs_file="$TEMP_DIR/$(basename "$repo_path")_refs.txt"
    get_all_refs "$repo_path" "$refs_file"

    if [[ ! -s "$refs_file" ]]; then
        log_warn "仓库 $(basename "$repo_path") 没有分支或标签"
        return 0
    fi

    # 扫描每个分支和标签
    while IFS=: read -r ref_type ref_name; do
        search_in_ref "$repo_path" "$ref_type" "$ref_name" "$keywords" "$repo_result_file"
    done < "$refs_file"

    # 统计结果
    if [[ -f "$repo_result_file" ]]; then
        local count
        count=$(wc -l < "$repo_result_file" 2>/dev/null || echo "0")
        if [[ "$count" -gt 0 ]]; then
            log "仓库 $(basename "$repo_path") 找到 $count 个匹配项"
        fi
    fi
}

# 合并所有结果
merge_results() {
    local final_output="$1"

    # 创建输出文件头部
    {
        echo "# Git仓库关键字扫描结果"
        echo "# 扫描时间: $(date)"
        echo "# 扫描目录: $BASE_DIR"
        echo "# 搜索关键字: $KEYWORDS"
        echo "# 格式: 仓库名|引用类型|引用名称|文件路径|行号|关键字|行内容"
        echo "#"
        echo "# ================================================================"
    } > "$final_output"

    # 合并所有结果文件
    find "$TEMP_DIR" -name "*_results.txt" -exec cat {} \; | sort >> "$final_output"
}

# 生成统计报告
generate_report() {
    local results_file="$1"
    local report_file="${results_file%.txt}_report.txt"

    if [[ ! -f "$results_file" ]] || [[ ! -s "$results_file" ]]; then
        log "没有找到匹配结果"
        return 0
    fi

    # 过滤掉注释行
    local data_lines
    data_lines=$(grep -v "^#" "$results_file")

    if [[ -z "$data_lines" ]]; then
        log "没有找到匹配结果"
        return 0
    fi

    {
        echo "==============================================="
        echo "Git仓库关键字扫描统计报告"
        echo "==============================================="
        echo "扫描时间: $(date)"
        echo "扫描目录: $BASE_DIR"
        echo "搜索关键字: $KEYWORDS"
        echo ""

        echo "总体统计:"
        echo "----------------------------------------"
        local total_matches
        total_matches=$(echo "$data_lines" | wc -l)
        echo "总匹配数: $total_matches"

        local unique_repos
        unique_repos=$(echo "$data_lines" | cut -d'|' -f1 | sort | uniq | wc -l)
        echo "涉及仓库数: $unique_repos"

        local unique_files
        unique_files=$(echo "$data_lines" | cut -d'|' -f4 | sort | uniq | wc -l)
        echo "涉及文件数: $unique_files"
        echo ""

        echo "按仓库统计 (Top 10):"
        echo "----------------------------------------"
        echo "$data_lines" | cut -d'|' -f1 | sort | uniq -c | sort -nr | head -10 | while read -r count repo; do
            printf "%-30s %s\n" "$repo" "$count"
        done
        echo ""

        echo "按关键字统计:"
        echo "----------------------------------------"
        echo "$data_lines" | cut -d'|' -f6 | sort | uniq -c | sort -nr | while read -r count keyword; do
            printf "%-30s %s\n" "$keyword" "$count"
        done
        echo ""

        echo "按文件类型统计 (Top 10):"
        echo "----------------------------------------"
        echo "$data_lines" | cut -d'|' -f4 | sed 's/.*\.//' | sort | uniq -c | sort -nr | head -10 | while read -r count ext; do
            printf "%-10s %s\n" "$ext" "$count"
        done
        echo ""

        echo "详细匹配结果 (前20条):"
        echo "----------------------------------------"
        echo "$data_lines" | head -20 | while IFS='|' read -r repo ref_type ref_name file line_num keyword line_content; do
            echo "仓库: $repo"
            echo "  $ref_type: $ref_name"
            echo "  文件: $file:$line_num"
            echo "  关键字: $keyword"
            echo "  内容: ${line_content:0:100}..."
            echo ""
        done

    } > "$report_file"

    log "统计报告已生成: $report_file"

    # 显示简要统计
    echo ""
    echo "==============================================="
    echo "扫描完成！"
    echo "==============================================="
    echo "总匹配数: $total_matches"
    echo "涉及仓库数: $unique_repos"
    echo "涉及文件数: $unique_files"
    echo "结果文件: $results_file"
    echo "报告文件: $report_file"
    echo "日志文件: $LOG_FILE"
}

# 主函数
main() {
    log "开始Git仓库关键字扫描"

    # 解析参数
    parse_args "$@"

    # 设置临时目录
    setup_temp_dir

    # 查找所有Git仓库
    local repos
    repos=$(find_git_repos "$BASE_DIR")

    if [[ -z "$repos" ]]; then
        log_error "在 $BASE_DIR 中未找到任何Git仓库"
        exit 1
    fi

    local repo_count
    repo_count=$(echo "$repos" | wc -l)
    log "找到 $repo_count 个Git仓库"

    # 并行扫描所有仓库
    echo "$repos" | xargs -I {} -P "$PARALLEL_JOBS" bash -c '
        source "'"$0"'"
        scan_repository "{}" "'"$KEYWORDS"'"
    '

    # 合并结果
    log "正在合并扫描结果..."
    merge_results "$OUTPUT_FILE"

    # 生成报告
    log "正在生成统计报告..."
    generate_report "$OUTPUT_FILE"

    log "扫描任务完成"
}

# 执行主函数
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
