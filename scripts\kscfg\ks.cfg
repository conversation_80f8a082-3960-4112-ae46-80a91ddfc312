#version=DEVEL
# Use graphical install
graphical


%packages
@^minimal-environment

%end

# Keyboard layouts
keyboard --xlayouts='us'
# System language
lang en_US.UTF-8

# Network information
network  --hostname=localhost.localdomain

# Use CDROM installation media
cdrom

# Run the Setup Agent on first boot
firstboot --enable
# System services
services --enabled="chronyd"
selinux --disabled


ignoredisk --only-use=sda
# Partition clearing information
clearpart --none --initlabel

# 使用预安装脚本动态生成分区配置
%pre --interpreter=/bin/bash --log=/tmp/ks-pre.log
# 获取磁盘大小（以MB为单位）
DISK_SIZE=$(lsblk -b -n -d -o SIZE /dev/sda | awk '{printf "%.0f", $1/1024/1024}')
PART_FILE="/tmp/part-include.cfg"

# 根据磁盘大小选择不同的分区配置
if [ $DISK_SIZE -ge 900000 ]; then
    # 960G磁盘配置
    cat > $PART_FILE << EOF
# 960G磁盘分区配置
part pv.930 --fstype="lvmpv" --ondisk=sda --size=900000 --grow
part /boot --fstype="ext4" --ondisk=sda --size=2048
part /boot/efi --fstype="efi" --ondisk=sda --size=512 --fsoptions="umask=0077,shortname=winnt"
volgroup openeuler --pesize=4096 pv.930
logvol / --fstype="ext4" --size=81920 --name=root --vgname=openeuler
logvol /var --fstype="ext4" --size=40960 --name=var --vgname=openeuler
logvol /home --fstype="ext4" --size=20480 --name=home --vgname=openeuler
logvol /var/log --fstype="ext4" --size=204800 --name=var_log --vgname=openeuler
logvol /var/lib/AnyBackup/spaceactivity --fstype="ext4" --size=102400 --name=spaceactivity --vgname=openeuler
logvol /backupsoft --fstype="ext4" --size=204800 --name=backupsoft --vgname=openeuler
logvol /backupdata --fstype="ext4" --size=40960 --name=backupdata --vgname=openeuler
logvol /backupIndex --fstype="ext4" --size=40960 --name=backupIndex --vgname=openeuler
EOF
else
    # 480G磁盘配置（使用原始配置）
    cat > $PART_FILE << EOF
# 480G磁盘分区配置
part pv.930 --fstype="lvmpv" --ondisk=sda --size=450560 --grow
part /boot --fstype="ext4" --ondisk=sda --size=2048
part /boot/efi --fstype="efi" --ondisk=sda --size=512 --fsoptions="umask=0077,shortname=winnt"
volgroup openeuler --pesize=4096 pv.930
logvol / --fstype="ext4" --size=40960 --name=root --vgname=openeuler
logvol /var --fstype="ext4" --size=40960 --name=var --vgname=openeuler
logvol /home --fstype="ext4" --size=10240 --name=home --vgname=openeuler
logvol /var/log --fstype="ext4" --size=102400 --name=var_log --vgname=openeuler
logvol /var/lib/AnyBackup/spaceactivity --fstype="ext4" --size=102400 --name=spaceactivity --vgname=openeuler
logvol /backupsoft --fstype="ext4" --size=40960 --name=backupsoft --vgname=openeuler
logvol /backupdata --fstype="ext4" --size=40960 --name=backupdata --vgname=openeuler
logvol /backupIndex --fstype="ext4" --size=40960 --name=backupIndex --vgname=openeuler
EOF
fi

# 记录日志，便于调试
echo "Detected disk size: ${DISK_SIZE}MB" >> /tmp/disk-size.log
cat $PART_FILE >> /tmp/disk-size.log
%end

# 引用动态生成的分区配置
%include /tmp/part-include.cfg

# System timezone
timezone Asia/Shanghai --utc

# Root password
rootpw --iscrypted $6$WR6vUTzBDayfN.2Y$Y.aBVxPls402ZgnVsy6XgCEzKPj38I4fXmYwAivaB.mGBbKxXBsFNMPs5MY6We0bR6kL6vjP0BZTEMf0AZMU31
# User euler
user --name=euler --password=$6$cZLPsh8w8AHu93./$Ys3wlArO.VFENJeJR3esXnMWUtPNjkqxAGgwEKy7WdsFXhHGIXHuviIvzz/R/LayYkafKNDqcImsFOgAkTVTw/ --iscrypted --gecos="euler"

# Reboot System
reboot

%addon com_redhat_kdump --disable --reserve-mb='128'

%end

%anaconda
pwpolicy root --minlen=8 --minquality=1 --strict --nochanges --notempty
pwpolicy user --minlen=8 --minquality=1 --strict --nochanges --emptyok
pwpolicy luks --minlen=8 --minquality=1 --strict --nochanges --notempty
%end

