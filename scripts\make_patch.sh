#!/bin/bash

SYS_NAME=`uname -p`
SHELL_PATH=$(cd "$(dirname "$0")";pwd)
ABSOLUTE_PATH=$(dirname ${SHELL_PATH})
BUILD_SRC=$(mkdir -p "${ABSOLUTE_PATH}/build";cd "${ABSOLUTE_PATH}/build"; pwd)


cd ${SHELL_PATH}

/bin/rm -rf /root/rpmbuild
mkdir -p /root/rpmbuild/{BUILD,BUILDROOT,RPMS,SOURCES,SPECS,SRPMS}

/bin/cp -rf SPECS/euler-patch.spec /root/rpmbuild/SPECS


mkdir -p /root/rpmbuild/BUILD/etc/.os/

/bin/cp -rf ${SHELL_PATH}/patch /root/rpmbuild/BUILD/etc/.os/

rpmbuild -ba /root/rpmbuild/SPECS/euler-patch.spec

/bin/cp -rf /root/rpmbuild/RPMS/`uname -p`/openEuler-patch-1.0-1.oe2203.`uname -p`.rpm ${BUILD_SRC}/Packages

