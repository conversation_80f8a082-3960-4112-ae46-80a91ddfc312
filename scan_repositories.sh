#!/bin/bash
# DevOps脚本：检测代码仓库中的关键字
# 扫描指定目录下所有Git仓库的所有分支和标签中的文件内容，查找指定关键字

set -e

# 默认配置
BASE_DIR=""
KEYWORDS=()
OUTPUT_FILE=""
MAX_DEPTH=3
VERBOSE=false
PARALLEL_JOBS=4

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_debug() {
    if [ "$VERBOSE" = true ]; then
        echo -e "${BLUE}[DEBUG]${NC} $1"
    fi
}

# 显示帮助信息
show_help() {
    cat << EOF
用法: $0 [选项] <目录路径>

选项:
    -k, --keywords KEYWORDS     要搜索的关键字（用逗号分隔）
    -f, --keywords-file FILE    包含关键字的文件（每行一个关键字）
    -o, --output FILE           输出结果到文件
    -d, --max-depth DEPTH       最大搜索深度（默认: 3）
    -j, --jobs JOBS             并行作业数（默认: 4）
    -v, --verbose               详细输出
    -h, --help                  显示此帮助信息

示例:
    $0 -k "devops.aishu.cn,greed" /path/to/repositories
    $0 -f keywords.txt -o results.txt /path/to/repositories
    $0 -k "sensitive_keyword" -v /path/to/repositories

关键字文件格式:
    每行一个关键字，支持正则表达式
    # 开头的行为注释
EOF
}

# 解析命令行参数
parse_args() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            -k|--keywords)
                IFS=',' read -ra KEYWORDS <<< "$2"
                shift 2
                ;;
            -f|--keywords-file)
                if [[ -f "$2" ]]; then
                    while IFS= read -r line; do
                        # 跳过空行和注释
                        if [[ -n "$line" && ! "$line" =~ ^[[:space:]]*# ]]; then
                            KEYWORDS+=("$line")
                        fi
                    done < "$2"
                else
                    log_error "关键字文件不存在: $2"
                    exit 1
                fi
                shift 2
                ;;
            -o|--output)
                OUTPUT_FILE="$2"
                shift 2
                ;;
            -d|--max-depth)
                MAX_DEPTH="$2"
                shift 2
                ;;
            -j|--jobs)
                PARALLEL_JOBS="$2"
                shift 2
                ;;
            -v|--verbose)
                VERBOSE=true
                shift
                ;;
            -h|--help)
                show_help
                exit 0
                ;;
            -*)
                log_error "未知选项: $1"
                show_help
                exit 1
                ;;
            *)
                if [[ -z "$BASE_DIR" ]]; then
                    BASE_DIR="$1"
                else
                    log_error "只能指定一个目录"
                    exit 1
                fi
                shift
                ;;
        esac
    done

    # 验证参数
    if [[ -z "$BASE_DIR" ]]; then
        log_error "必须指定要扫描的目录"
        show_help
        exit 1
    fi

    if [[ ! -d "$BASE_DIR" ]]; then
        log_error "目录不存在: $BASE_DIR"
        exit 1
    fi

    if [[ ${#KEYWORDS[@]} -eq 0 ]]; then
        log_error "必须指定要搜索的关键字"
        show_help
        exit 1
    fi
}

# 查找Git仓库
find_git_repositories() {
    local base_dir="$1"
    local max_depth="$2"
    
    log_info "在 $base_dir 中查找Git仓库（最大深度: $max_depth）..."
    
    find "$base_dir" -maxdepth "$max_depth" -type d -name ".git" | while read -r git_dir; do
        echo "$(dirname "$git_dir")"
    done
}

# 获取仓库的分支和标签
get_refs() {
    local repo_path="$1"
    local refs=()
    
    cd "$repo_path" || return 1
    
    # 获取所有分支
    while IFS= read -r branch; do
        branch=$(echo "$branch" | sed 's/^[* ] *//' | sed 's/remotes\/origin\///')
        if [[ "$branch" != "HEAD" && "$branch" != "" ]]; then
            refs+=("branch:$branch")
        fi
    done < <(git branch -a 2>/dev/null || true)
    
    # 获取所有标签
    while IFS= read -r tag; do
        if [[ -n "$tag" ]]; then
            refs+=("tag:$tag")
        fi
    done < <(git tag 2>/dev/null || true)
    
    printf '%s\n' "${refs[@]}"
}

# 在指定引用中搜索关键字
search_in_ref() {
    local repo_path="$1"
    local ref_type="$2"
    local ref_name="$3"
    local keywords=("${@:4}")
    
    cd "$repo_path" || return 1
    
    log_debug "扫描 $repo_path 的 $ref_type: $ref_name"
    
    # 保存当前分支
    local current_branch
    current_branch=$(git rev-parse --abbrev-ref HEAD 2>/dev/null || echo "")
    
    # 切换到指定引用
    if ! git checkout "$ref_name" >/dev/null 2>&1; then
        log_warn "无法切换到 $ref_type: $ref_name in $repo_path"
        return 1
    fi
    
    # 获取所有文件
    local files
    files=$(git ls-files 2>/dev/null || true)
    
    if [[ -z "$files" ]]; then
        # 恢复原分支
        if [[ -n "$current_branch" && "$current_branch" != "HEAD" ]]; then
            git checkout "$current_branch" >/dev/null 2>&1 || true
        fi
        return 0
    fi
    
    # 搜索每个文件
    echo "$files" | while IFS= read -r file; do
        if [[ -f "$file" ]]; then
            # 检查文件大小（跳过大文件）
            local file_size
            file_size=$(stat -f%z "$file" 2>/dev/null || stat -c%s "$file" 2>/dev/null || echo "0")
            if [[ "$file_size" -gt 10485760 ]]; then  # 10MB
                continue
            fi
            
            # 检查是否为文本文件
            if file "$file" | grep -q "text\|empty"; then
                for keyword in "${keywords[@]}"; do
                    local matches
                    matches=$(grep -n -i "$keyword" "$file" 2>/dev/null || true)
                    if [[ -n "$matches" ]]; then
                        echo "$matches" | while IFS= read -r match; do
                            local line_num
                            local line_content
                            line_num=$(echo "$match" | cut -d: -f1)
                            line_content=$(echo "$match" | cut -d: -f2-)
                            
                            printf "MATCH|%s|%s|%s|%s|%s|%s|%s\n" \
                                "$(basename "$repo_path")" \
                                "$ref_type" \
                                "$ref_name" \
                                "$file" \
                                "$line_num" \
                                "$keyword" \
                                "$line_content"
                        done
                    fi
                done
            fi
        fi
    done
    
    # 恢复原分支
    if [[ -n "$current_branch" && "$current_branch" != "HEAD" ]]; then
        git checkout "$current_branch" >/dev/null 2>&1 || true
    fi
}

# 扫描单个仓库
scan_repository() {
    local repo_path="$1"
    local keywords=("${@:2}")
    
    log_info "扫描仓库: $(basename "$repo_path")"
    
    if [[ ! -d "$repo_path/.git" ]]; then
        log_warn "不是有效的Git仓库: $repo_path"
        return 1
    fi
    
    # 获取所有引用
    local refs
    refs=$(get_refs "$repo_path")
    
    if [[ -z "$refs" ]]; then
        log_warn "仓库中没有找到分支或标签: $repo_path"
        return 0
    fi
    
    # 扫描每个引用
    echo "$refs" | while IFS= read -r ref; do
        local ref_type
        local ref_name
        ref_type=$(echo "$ref" | cut -d: -f1)
        ref_name=$(echo "$ref" | cut -d: -f2-)
        
        search_in_ref "$repo_path" "$ref_type" "$ref_name" "${keywords[@]}"
    done
}

# 主扫描函数
scan_all_repositories() {
    local base_dir="$1"
    local keywords=("${@:2}")
    
    log_info "开始扫描目录: $base_dir"
    log_info "搜索关键字: ${keywords[*]}"
    
    # 查找所有Git仓库
    local repos
    repos=$(find_git_repositories "$base_dir" "$MAX_DEPTH")
    
    if [[ -z "$repos" ]]; then
        log_warn "未找到任何Git仓库"
        return 0
    fi
    
    local repo_count
    repo_count=$(echo "$repos" | wc -l)
    log_info "找到 $repo_count 个Git仓库"
    
    # 创建临时文件存储结果
    local temp_results
    temp_results=$(mktemp)
    
    # 并行扫描仓库
    echo "$repos" | xargs -I {} -P "$PARALLEL_JOBS" bash -c "
        source '$0'
        scan_repository '{}' ${keywords[*]@Q}
    " >> "$temp_results"
    
    # 处理结果
    if [[ -s "$temp_results" ]]; then
        local match_count
        match_count=$(wc -l < "$temp_results")
        log_info "扫描完成，共找到 $match_count 个匹配项"
        
        # 输出结果
        if [[ -n "$OUTPUT_FILE" ]]; then
            {
                echo "# 扫描结果 - $(date)"
                echo "# 格式: 仓库|引用类型|引用名称|文件路径|行号|关键字|行内容"
                echo "# ========================================"
                sort "$temp_results"
            } > "$OUTPUT_FILE"
            log_info "结果已保存到: $OUTPUT_FILE"
        fi
        
        # 显示摘要
        show_summary "$temp_results"
    else
        log_info "未找到任何匹配项"
    fi
    
    # 清理临时文件
    rm -f "$temp_results"
}

# 显示结果摘要
show_summary() {
    local results_file="$1"
    
    echo
    echo "========================================"
    echo "扫描结果摘要"
    echo "========================================"
    
    # 按仓库统计
    echo "按仓库统计:"
    cut -d'|' -f1 "$results_file" | sort | uniq -c | sort -nr | head -10
    
    echo
    echo "按关键字统计:"
    cut -d'|' -f6 "$results_file" | sort | uniq -c | sort -nr
    
    echo
    echo "详细结果 (前20条):"
    echo "----------------------------------------"
    head -20 "$results_file" | while IFS='|' read -r repo ref_type ref_name file line_num keyword line_content; do
        echo "仓库: $repo"
        echo "  $ref_type: $ref_name"
        echo "  文件: $file:$line_num"
        echo "  关键字: $keyword"
        echo "  内容: ${line_content:0:100}..."
        echo
    done
}

# 主函数
main() {
    parse_args "$@"
    scan_all_repositories "$BASE_DIR" "${KEYWORDS[@]}"
}

# 如果直接执行此脚本
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
