#!/bin/bash

export PROJECT=${PRODUCT}
SYS_NAME=`uname -p`

#dir path
SHELL_PATH=$(cd "$(dirname "$0")";pwd)
ABSOLUTE_PATH=$(dirname ${SHELL_PATH})
OpenEuler_SRC=$(cd "${ABSOLUTE_PATH}/openEuler-22.03-LTS-${SYS_NAME}"; pwd)
BUILD_SRC=$(mkdir -p "${ABSOLUTE_PATH}/build";cd "${ABSOLUTE_PATH}/build"; pwd)
RELEASE_PATH=$(mkdir -p "${ABSOLUTE_PATH}/release";cd "${ABSOLUTE_PATH}/release"; pwd)


#verison
BUILD_TIME=`date "+%y%m%d-%H%M"`
BUILD_NUM="${BUILD_NUMBER}"
if [[ -z "${BUILD_NUM}" ]]; then 
    BUILD_NUM="X"
fi

if [[ -z "${PRODUCT}" ]];then
    PRODUCT=openEuler
fi

if [[ -z "${RELEASE_TYPE}" ]];then
    RELEASE_TYPE=standard
fi

OS_VER="${BUILD_TIME}-${BUILD_NUM}"
GIT_VER=`git rev-parse HEAD`
ISO_NAME="${PROJECT}-${RELEASE_TYPE}-${OS_VER}-${SYS_NAME}"

function showenv() {

    echo ""
    echo "***Build-Env"
    echo "PROJECT:${PROJECT}-${RELEASE_TYPE}"
    echo "SHELL_PATH:${SHELL_PATH}"
    echo "OpenEuler_SRC:${OpenEuler_SRC}"
    echo "BUILD_SRC:${BUILD_SRC}"
    echo "RELEASE_PATH:${RELEASE_PATH}"
    echo "BUILD_NUM:${BUILD_NUM}"
    echo "BUILD_TIME:${BUILD_TIME}"
    echo "OS_VER:${OS_VER}"
    echo "GIT_VER:${GIT_VER}"
    echo "ISO_NAME:${ISO_NAME}"
    echo ""

}

# make system_version
function sysversion() {
    echo "Project=${PROJECT}-${RELEASE_TYPE}"
    echo "OSVer=${OS_VER}"
    echo "OSGitVer=${GIT_VER}"
    echo "buildNum=${GIT_BRANCH}-${BUILD_NUMBER}"
    sed -i "s%OS-TYPE%${PROJECT}-${RELEASE_TYPE}%g" ${SHELL_PATH}/kscfg/post.cfg
    sed -i "s%OS-VER%${OS_VER}%g" ${SHELL_PATH}/kscfg/post.cfg
    sed -i "s%OS-GITVER%${GIT_VER}%g" ${SHELL_PATH}/kscfg/post.cfg
    sed -i "s%OS-BUILDNUN%${GIT_BRANCH}-${BUILD_NUMBER}%g" ${SHELL_PATH}/kscfg/post.cfg
}

function makeanybackup() {

    cd ${SHELL_PATH}

    if [ "${AB_MAIN_VERSION}"x == "6"x ]; then
        ./make_anybackup60_rpm.sh
    else
        ./make_anybackup70_rpm.sh
    fi
}

function makepatchrpm() {
    cd ${SHELL_PATH}
    ./make_patch.sh
}

function makerepo() {
    cd ${BUILD_SRC}
    createrepo -v -g repodata/normal.xml .
}

function makeiso() {
    cd ${BUILD_SRC}
    #镜像生成
    if [[ "${SYS_NAME}" == "aarch64" ]];then
        genisoimage -e images/efiboot.img -no-emul-boot -R -J -T -c boot.catalog -hide boot.catalog -V "OpenEulerOS" -o "${RELEASE_PATH}/${ISO_NAME}".iso .
    elif [[ "${SYS_NAME}" == "x86_64" ]];then
        genisoimage -v -cache-inodes -joliet-long -R -J -T -V "OpenEulerOS" \
        -o "${RELEASE_PATH}/${ISO_NAME}".iso -c isolinux/boot.cat -b isolinux/isolinux.bin \
        -no-emul-boot -boot-load-size 4 -boot-info-table -eltorito-alt-boot -b images/efiboot.img -no-emul-boot .
    else
        echo "Unrecognized platform"
        exit 1
    fi



    #make update-
    /bin/cp -rf ${BUILD_SRC}/Packages/openEuler-patch* ${RELEASE_PATH}
    
    #make isomd5sum os install check
    implantisomd5 ${RELEASE_PATH}/${ISO_NAME}.iso > "${RELEASE_PATH}/${ISO_NAME}".isomd5sum

    echo "makeiso success"
    echo ""
}

function makepxe() {
    mkdir -p ${BUILD_SRC}/tftpboot
    /bin/cp -rf ${BUILD_SRC}/images/pxeboot/{initrd.img,vmlinuz} ${BUILD_SRC}/tftpboot
    /bin/cp -rf ${BUILD_SRC}/isolinux/{boot.cat,vesamenu.c32,boot.msg} ${BUILD_SRC}/tftpboot

    mkdir -p "${RELEASE_PATH}"
    cd ${BUILD_SRC}/../	
    tar zcvf "${RELEASE_PATH}/${ISO_NAME}-pxe.tar.gz" build
    PXE_TAR_MD5=`md5sum "${RELEASE_PATH}/${ISO_NAME}-pxe.tar.gz" | cut -f 1 -d" "`
    echo ${PXE_TAR_MD5} > "${RELEASE_PATH}/${ISO_NAME}-pxe.md5"

    echo "path:${RELEASE_PATH}/${ISO_NAME}-pxe.tar.gz"
    echo "md5:${PXE_TAR_MD5}"
    echo ""
    echo ""
}

#show info
showenv

#clean old-release
/bin/rm -rf ${BUILD_SRC}
/bin/cp -rf ${OpenEuler_SRC} ${BUILD_SRC}
mkdir -p ${RELEASE_PATH}/${ISO_NAME}

#makepatchrpm
makepatchrpm

#make repo
makerepo

#添加系统信息
sysversion

#make ks.cfg
cd ${SHELL_PATH}
./make_kscfg.sh

makeiso


ls ${RELEASE_PATH}

#make md5
ISO_MD5=`md5sum "${RELEASE_PATH}/${ISO_NAME}".iso`
echo ${ISO_MD5} > "${RELEASE_PATH}/${ISO_NAME}".md5
#make sha256sum
ISO_SHA256=`sha256sum "${RELEASE_PATH}/${ISO_NAME}".iso`
echo ${ISO_SHA256} > "${RELEASE_PATH}/${ISO_NAME}".sha256
echo "path:${RELEASE_PATH}/${ISO_NAME}.iso"
echo "md5:${ISO_MD5}"
echo "sha256:${ISO_SHA256}"
chmod -R 755 ${RELEASE_PATH}
