#!/bin/bash
SYS_NAME=`uname -p`
SHELL_PATH=$(cd "$(dirname "$0")";pwd)
ABSOLUTE_PATH=$(dirname ${SHELL_PATH})
OpenEuler_SRC=$(cd "${ABSOLUTE_PATH}/openEuler-22.03-LTS-${SYS_NAME}"; pwd)
BUILD_SRC=$(mkdir -p "${ABSOLUTE_PATH}/build";cd "${ABSOLUTE_PATH}/build"; pwd)

#ks 无人值守
KS_CFG_DIR="isolinux/ks.conf"
EFI_CFG_DIR="efilinux/efi.conf"
KS_TYPE=ks.cfg
MANUAL_KS_TYPE=manualks.cfg



function showenv() {
	echo "OpenEuler_SRC:${OpenEuler_SRC}"
	echo "BUILD_SRC=${BUILD_SRC}"
	echo ""
}

function addmenu () {

###
if [[ ${SYS_NAME} == "x86_64" ]];then
    echo "ks-dir:${KS_CFG_DIR}"
    sed -i "s/KSMENNU/${1}.cfg/g" isolinux/isolinux.cfg
    sed -i "s/KSMENNU/${1}.cfg/g" EFI/BOOT/grub.cfg
    

###
elif [[ ${SYS_NAME} == "aarch64" ]];then

    echo "efi-dir:${EFI_CFG_DIR}"
    sed -i "s/KSMENNU/${1}.cfg/g" EFI/BOOT/grub.cfg

else

    echo "Unrecognized platform"
    exit 1

fi
}

function addmanualmenu () {

###
if [[ ${SYS_NAME} == "x86_64" ]];then
    echo "ks-dir:${KS_CFG_DIR}"
    sed -i "s/MANUALKS/${1}.cfg/g" isolinux/isolinux.cfg
    sed -i "s/MANUALKS/${1}.cfg/g" EFI/BOOT/grub.cfg
    

###
elif [[ ${SYS_NAME} == "aarch64" ]];then

    echo "efi-dir:${EFI_CFG_DIR}"
    sed -i "s/MANUALKS/${1}.cfg/g" EFI/BOOT/grub.cfg

else

    echo "Unrecognized platform"
    exit 1

fi
}

# make_isolinux
make_isolinux () {

    mkdir -p ${BUILD_SRC}/${KS_CFG_DIR}
    mkdir -p ${BUILD_SRC}/${EFI_CFG_DIR}

}

#append %post for 用来处理系统安装完成后安全加固&系统配置
function post() {
    cat ${SHELL_PATH}/kscfg/post.cfg
}

function make_all_kscfg() {
    #KS_CFG_DIR
    /bin/cp -rf ${SHELL_PATH}/kscfg/${KS_TYPE} ${BUILD_SRC}/${KS_CFG_DIR}/base.cfg

    /bin/cp -rf ${BUILD_SRC}/${KS_CFG_DIR}/base.cfg ${BUILD_SRC}/${KS_CFG_DIR}/${1}.cfg
    sed -i '/efi/d' ${BUILD_SRC}/${KS_CFG_DIR}/${1}.cfg
    post >> ${BUILD_SRC}/${KS_CFG_DIR}/${1}.cfg
    
    #EFI_CFG_DIR
    /bin/cp -rf ${BUILD_SRC}/${KS_CFG_DIR}/base.cfg ${BUILD_SRC}/${EFI_CFG_DIR}/${1}.cfg
    post >> ${BUILD_SRC}/${EFI_CFG_DIR}/${1}.cfg
    
    #修正kfg文件安装选项
    addmenu ${1}
}

function make_manual_kscfg() {
    #KS_CFG_DIR
    /bin/cp -rf ${SHELL_PATH}/kscfg/${MANUAL_KS_TYPE} ${BUILD_SRC}/${KS_CFG_DIR}/base.cfg

    /bin/cp -rf ${BUILD_SRC}/${KS_CFG_DIR}/base.cfg ${BUILD_SRC}/${KS_CFG_DIR}/${1}.cfg
    sed -i '/efi/d' ${BUILD_SRC}/${KS_CFG_DIR}/${1}.cfg
    post >> ${BUILD_SRC}/${KS_CFG_DIR}/${1}.cfg
    
    #EFI_CFG_DIR
    /bin/cp -rf ${BUILD_SRC}/${KS_CFG_DIR}/base.cfg ${BUILD_SRC}/${EFI_CFG_DIR}/${1}.cfg
    post >> ${BUILD_SRC}/${EFI_CFG_DIR}/${1}.cfg
    
    #修正kfg文件安装选项
    addmanualmenu ${1}
}

function make_release () {
    make_all_kscfg "openEuler-ks"
    make_manual_kscfg "openEuler-manualks"
}


cd ${BUILD_SRC}

showenv
make_isolinux
#使用原生加载
make_release
