# DevOps关键字配置文件
# 每行一个关键字，支持正则表达式
# 以 # 开头的行为注释

# 根据图片中的关键字，添加相关的搜索模式
devops.aishu.cn
AISHUDevOps
flora/_git/greed
greed

# 其他可能的敏感关键字
# 内部域名和服务
\.aishu\.cn
aishu\.com
internal\.
dev\.
test\.
staging\.

# Git相关的敏感信息
_git/
\.git/
github\.com/.*private
gitlab\.com/.*private

# 可能的敏感路径
/flora/
/devops/
/internal/
/private/

# API密钥和令牌模式
api[_-]?key
access[_-]?token
secret[_-]?key
private[_-]?key
auth[_-]?token

# 数据库连接信息
password\s*=
pwd\s*=
passwd\s*=
database[_-]?url
db[_-]?host
connection[_-]?string

# 服务器和主机信息
192\.168\.
10\.0\.
172\.16\.
localhost:
127\.0\.0\.1

# 邮箱和用户信息
@aishu\.cn
@aishu\.com
admin@
root@
test@

# 配置文件中的敏感信息
\.env
config\.json
settings\.ini
application\.properties
database\.yml
