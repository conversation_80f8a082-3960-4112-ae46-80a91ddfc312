#  This file is part of systemd.
#
#  systemd is free software; you can redistribute it and/or modify it under the
#  terms of the GNU Lesser General Public License as published by the Free
#  Software Foundation; either version 2.1 of the License, or (at your option)
#  any later version.
#
# Entries in this file show the compile time defaults. Local configuration
# should be created by either modifying this file, or by creating "drop-ins" in
# the coredump.conf.d/ subdirectory. The latter is generally recommended.
# Defaults can be restored by simply deleting this file and all drop-ins.
#
# Use 'systemd-analyze cat-config systemd/coredump.conf' to display the full config.
#
# See coredump.conf(5) for details.

[Coredump]
#Storage=external
#Compress=yes
ProcessSizeMax=2000G
ExternalSizeMax=2000G
#JournalSizeMax=767M
#MaxUse=
#KeepFree=
