#!/bin/bash

SHELL_PATH=$(cd "$(dirname "$0")";pwd)

cd ${SHELL_PATH}
#1.Optimize system open processes & files
/bin/cp -rf ./system_file/etc/security/limits.conf /etc/security/limits.conf


#2.
/bin/cp -rf ./system_file/etc/systemd/coredump.conf /etc/systemd/coredump.conf

#end
echo "success" >> /etc/.os/patch/install_patch.log 

#end clean rc.local
sed -i '/install_patch/d' /etc/rc.d/rc.local

reboot