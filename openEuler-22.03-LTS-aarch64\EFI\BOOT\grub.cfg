set default="1"

function load_video {
  if [ x$feature_all_video_module = xy ]; then
    insmod all_video
  else
    insmod efi_gop
    insmod efi_uga
    insmod ieee1275_fb
    insmod vbe
    insmod vga
    insmod video_bochs
    insmod video_cirrus
  fi
}

load_video
set gfxpayload=keep
insmod gzio
insmod part_gpt
insmod ext2

set timeout=60
### END /etc/grub.d/00_header ###

search --no-floppy --set=root -l 'openEuler-22.03-LTS-aarch64'

### BEGIN /etc/grub.d/10_linux ###
menuentry 'AutoInstall openEuler 22.03-LTS' --class red --class gnu-linux --class gnu --class os {
    linux /images/pxeboot/vmlinuz inst.ks=cdrom:/efilinux/efi.conf/KSMENNU fpi_to_tail=off  
    initrd /images/pxeboot/initrd.img
}
menuentry 'Test this media & AutoInstall openEuler 22.03-LTS' --class red --class gnu-linux --class gnu --class os {
    linux /images/pxeboot/vmlinuz inst.ks=cdrom:/efilinux/efi.conf/KSMENNU rd.live.check fpi_to_tail=off  
    initrd /images/pxeboot/initrd.img
}
menuentry 'Install openEuler 22.03-LTS' --class red --class gnu-linux --class gnu --class os {
    linux /images/pxeboot/vmlinuz inst.ks=cdrom:/efilinux/efi.conf/MANUALKS fpi_to_tail=off  
    initrd /images/pxeboot/initrd.img
}
menuentry 'Test this media & Install openEuler 22.03-LTS' --class red --class gnu-linux --class gnu --class os {
    linux /images/pxeboot/vmlinuz inst.ks=cdrom:/efilinux/efi.conf/MANUALKS rd.live.check fpi_to_tail=off  
    initrd /images/pxeboot/initrd.img
}
#menuentry 'Install openEuler 22.03-LTS' --class red --class gnu-linux --class gnu --class os {
#	linux /images/pxeboot/vmlinuz inst.stage2=hd:LABEL=openEuler-22.03-LTS-aarch64 ro inst.geoloc=0 console=tty0 smmu.bypassdev=0x1000:0x17 smmu.bypassdev=0x1000:0x15 video=efifb:off video=VGA-1:640x480-32@60me fpi_to_tail=off
#	initrd /images/pxeboot/initrd.img
#}
#menuentry 'Test this media & install openEuler 22.03-LTS' --class red --class gnu-linux --class gnu --class os {
#	linux /images/pxeboot/vmlinuz inst.stage2=hd:LABEL=openEuler-22.03-LTS-aarch64 rd.live.check inst.geoloc=0 console=tty0 smmu.bypassdev=0x1000:0x17 smmu.bypassdev=0x1000:0x15 video=efifb:off video=VGA-1:640x480-32@60me fpi_to_tail=off
#	initrd /images/pxeboot/initrd.img
#}
submenu 'Troubleshooting -->' {
	menuentry 'Install openEuler 22.03-LTS in basic graphics mode' --class red --class gnu-linux --class gnu --class os {
		linux /images/pxeboot/vmlinuz inst.stage2=hd:LABEL=openEuler-22.03-LTS-aarch64 nomodeset inst.geoloc=0 console=tty0 smmu.bypassdev=0x1000:0x17 smmu.bypassdev=0x1000:0x15 video=efifb:off video=VGA-1:640x480-32@60me fpi_to_tail=off
		initrd /images/pxeboot/initrd.img
	}
	menuentry 'Rescue the openEuler system' --class red --class gnu-linux --class gnu --class os {
		linux /images/pxeboot/vmlinuz inst.stage2=hd:LABEL=openEuler-22.03-LTS-aarch64 rescue console=tty0 smmu.bypassdev=0x1000:0x17 smmu.bypassdev=0x1000:0x15 video=efifb:off video=VGA-1:640x480-32@60me fpi_to_tail=off
		initrd /images/pxeboot/initrd.img
	}
}
