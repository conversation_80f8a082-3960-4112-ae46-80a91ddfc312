#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DevOps脚本：检测代码仓库中的关键字
扫描指定目录下所有Git仓库的所有分支和标签中的文件内容，查找指定关键字
"""

import os
import sys
import subprocess
import re
import json
import argparse
from pathlib import Path
from datetime import datetime
import threading
import queue
from concurrent.futures import ThreadPoolExecutor, as_completed

class RepositoryScanner:
    def __init__(self, base_dir, keywords, output_file=None, max_workers=4):
        """
        初始化仓库扫描器

        Args:
            base_dir: 要扫描的基础目录
            keywords: 要搜索的关键字列表
            output_file: 输出文件路径
            max_workers: 最大并发工作线程数
        """
        self.base_dir = Path(base_dir)
        self.keywords = keywords if isinstance(keywords, list) else [keywords]
        self.output_file = output_file
        self.max_workers = max_workers
        self.results = []
        self.lock = threading.Lock()

        # 编译正则表达式以提高搜索效率（区分大小写）
        self.keyword_patterns = [re.compile(keyword) for keyword in self.keywords]

    def find_git_repositories(self):
        """查找所有Git仓库"""
        git_repos = []
        for root, dirs, files in os.walk(self.base_dir):
            if '.git' in dirs:
                git_repos.append(Path(root))
                # 不再递归进入.git目录
                dirs.remove('.git')
        return git_repos

    def run_git_command(self, repo_path, command):
        """在指定仓库中执行Git命令"""
        try:
            result = subprocess.run(
                command,
                cwd=repo_path,
                capture_output=True,
                text=True,
                timeout=30
            )
            if result.returncode == 0:
                return result.stdout.strip()
            else:
                print(f"Git命令执行失败 in {repo_path}: {result.stderr}")
                return None
        except subprocess.TimeoutExpired:
            print(f"Git命令超时 in {repo_path}: {' '.join(command)}")
            return None
        except Exception as e:
            print(f"执行Git命令时出错 in {repo_path}: {e}")
            return None

    def get_branches_and_tags(self, repo_path):
        """获取仓库的所有分支和标签"""
        branches = []
        tags = []

        # 获取所有分支
        branch_output = self.run_git_command(repo_path, ['git', 'branch', '-a'])
        if branch_output:
            for line in branch_output.split('\n'):
                line = line.strip()
                if line and not line.startswith('*'):
                    # 清理分支名称
                    branch = line.replace('remotes/origin/', '').replace('remotes/', '')
                    if branch not in branches and branch != 'HEAD':
                        branches.append(branch)

        # 获取所有标签
        tag_output = self.run_git_command(repo_path, ['git', 'tag'])
        if tag_output:
            tags = [tag.strip() for tag in tag_output.split('\n') if tag.strip()]

        return branches, tags

    def search_in_file(self, file_path, content):
        """在文件内容中搜索关键字"""
        matches = []
        try:
            lines = content.split('\n')
            for line_num, line in enumerate(lines, 1):
                for i, pattern in enumerate(self.keyword_patterns):
                    if pattern.search(line):
                        matches.append({
                            'keyword': self.keywords[i],
                            'line_number': line_num,
                            'line_content': line.strip(),
                            'file_path': str(file_path)
                        })
        except Exception as e:
            print(f"搜索文件内容时出错 {file_path}: {e}")

        return matches

    def scan_ref(self, repo_path, ref_name, ref_type):
        """扫描指定分支或标签"""
        results = []

        try:
            # 切换到指定分支或标签
            checkout_result = self.run_git_command(repo_path, ['git', 'checkout', ref_name])
            if checkout_result is None:
                return results

            # 获取所有文件列表
            file_list = self.run_git_command(repo_path, ['git', 'ls-files'])
            if not file_list:
                return results

            files = file_list.split('\n')

            for file_path in files:
                if not file_path.strip():
                    continue

                full_path = repo_path / file_path

                # 跳过二进制文件和大文件
                try:
                    if full_path.stat().st_size > 10 * 1024 * 1024:  # 跳过大于10MB的文件
                        continue
                except:
                    continue

                try:
                    # 读取文件内容
                    with open(full_path, 'r', encoding='utf-8', errors='ignore') as f:
                        content = f.read()

                    # 搜索关键字
                    matches = self.search_in_file(file_path, content)

                    for match in matches:
                        result = {
                            'repository': str(repo_path.relative_to(self.base_dir)),
                            'ref_type': ref_type,
                            'ref_name': ref_name,
                            'file_path': match['file_path'],
                            'keyword': match['keyword'],
                            'line_number': match['line_number'],
                            'line_content': match['line_content'],
                            'timestamp': datetime.now().isoformat()
                        }
                        results.append(result)

                except Exception as e:
                    # 跳过无法读取的文件（可能是二进制文件）
                    continue

        except Exception as e:
            print(f"扫描 {ref_type} {ref_name} 时出错 in {repo_path}: {e}")

        return results

    def scan_repository(self, repo_path):
        """扫描单个仓库"""
        print(f"正在扫描仓库: {repo_path}")
        repo_results = []

        try:
            # 保存当前分支
            current_branch = self.run_git_command(repo_path, ['git', 'rev-parse', '--abbrev-ref', 'HEAD'])

            # 获取所有分支和标签
            branches, tags = self.get_branches_and_tags(repo_path)

            # 扫描所有分支
            for branch in branches:
                print(f"  扫描分支: {branch}")
                branch_results = self.scan_ref(repo_path, branch, 'branch')
                repo_results.extend(branch_results)

            # 扫描所有标签
            for tag in tags:
                print(f"  扫描标签: {tag}")
                tag_results = self.scan_ref(repo_path, tag, 'tag')
                repo_results.extend(tag_results)

            # 恢复原始分支
            if current_branch and current_branch != 'HEAD':
                self.run_git_command(repo_path, ['git', 'checkout', current_branch])

        except Exception as e:
            print(f"扫描仓库时出错 {repo_path}: {e}")

        return repo_results

    def scan_all_repositories(self):
        """扫描所有仓库"""
        print(f"开始扫描目录: {self.base_dir}")
        print(f"搜索关键字: {', '.join(self.keywords)}")

        # 查找所有Git仓库
        git_repos = self.find_git_repositories()
        print(f"找到 {len(git_repos)} 个Git仓库")

        if not git_repos:
            print("未找到任何Git仓库")
            return

        # 使用线程池并行扫描
        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            future_to_repo = {executor.submit(self.scan_repository, repo): repo for repo in git_repos}

            for future in as_completed(future_to_repo):
                repo = future_to_repo[future]
                try:
                    repo_results = future.result()
                    with self.lock:
                        self.results.extend(repo_results)
                except Exception as e:
                    print(f"处理仓库 {repo} 时出错: {e}")

        print(f"扫描完成，共找到 {len(self.results)} 个匹配项")

    def save_results(self):
        """保存扫描结果"""
        if not self.results:
            print("没有找到任何匹配项")
            return

        # 按仓库、分支/标签、文件路径排序
        self.results.sort(key=lambda x: (x['repository'], x['ref_name'], x['file_path'], x['line_number']))

        if self.output_file:
            # 保存为JSON格式
            with open(self.output_file, 'w', encoding='utf-8') as f:
                json.dump(self.results, f, ensure_ascii=False, indent=2)
            print(f"结果已保存到: {self.output_file}")

        # 打印摘要
        self.print_summary()

    def print_summary(self):
        """打印扫描结果摘要"""
        print("\n" + "="*80)
        print("扫描结果摘要")
        print("="*80)

        # 按仓库分组统计
        repo_stats = {}
        for result in self.results:
            repo = result['repository']
            if repo not in repo_stats:
                repo_stats[repo] = {'branches': set(), 'tags': set(), 'files': set(), 'matches': 0}

            if result['ref_type'] == 'branch':
                repo_stats[repo]['branches'].add(result['ref_name'])
            else:
                repo_stats[repo]['tags'].add(result['ref_name'])

            repo_stats[repo]['files'].add(result['file_path'])
            repo_stats[repo]['matches'] += 1

        for repo, stats in repo_stats.items():
            print(f"\n仓库: {repo}")
            print(f"  匹配的分支数: {len(stats['branches'])}")
            print(f"  匹配的标签数: {len(stats['tags'])}")
            print(f"  匹配的文件数: {len(stats['files'])}")
            print(f"  总匹配次数: {stats['matches']}")

        # 打印详细结果（限制数量）
        print(f"\n详细匹配结果 (显示前50条):")
        print("-" * 80)

        for i, result in enumerate(self.results[:50]):
            print(f"{i+1}. 仓库: {result['repository']}")
            print(f"   {result['ref_type']}: {result['ref_name']}")
            print(f"   文件: {result['file_path']}:{result['line_number']}")
            print(f"   关键字: {result['keyword']}")
            print(f"   内容: {result['line_content'][:100]}...")
            print()

def main():
    parser = argparse.ArgumentParser(description='扫描Git仓库中的关键字')
    parser.add_argument('directory', help='要扫描的目录路径')
    parser.add_argument('-k', '--keywords', nargs='+', required=True, help='要搜索的关键字')
    parser.add_argument('-o', '--output', help='输出文件路径')
    parser.add_argument('-w', '--workers', type=int, default=4, help='并发工作线程数')

    args = parser.parse_args()

    if not os.path.exists(args.directory):
        print(f"错误: 目录 {args.directory} 不存在")
        sys.exit(1)

    scanner = RepositoryScanner(
        base_dir=args.directory,
        keywords=args.keywords,
        output_file=args.output,
        max_workers=args.workers
    )

    try:
        scanner.scan_all_repositories()
        scanner.save_results()
    except KeyboardInterrupt:
        print("\n扫描被用户中断")
    except Exception as e:
        print(f"扫描过程中出错: {e}")

if __name__ == '__main__':
    main()
