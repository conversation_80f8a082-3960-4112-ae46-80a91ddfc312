#!/bin/bash
# 测试脚本 - 验证关键字搜索的大小写敏感性

echo "=== 大小写敏感性测试 ==="

# 创建测试目录和文件
TEST_DIR="test_case_sensitivity"
mkdir -p "$TEST_DIR"

# 创建测试文件
cat > "$TEST_DIR/test_file.txt" << 'EOF'
这是一个测试文件，包含不同大小写的关键字：

devops.aishu.cn - 小写版本
DEVOPS.AISHU.CN - 大写版本
DevOps.AiShu.Cn - 混合大小写版本

greed - 小写
GREED - 大写
Greed - 首字母大写

AISHUDevOps - 原始格式
aishudevops - 全小写
AISHUDEVOPS - 全大写
EOF

echo "测试文件内容："
echo "----------------------------------------"
cat "$TEST_DIR/test_file.txt"
echo "----------------------------------------"
echo ""

# 测试不同的grep命令
echo "1. 使用 grep -n 'devops.aishu.cn' (区分大小写):"
grep -n 'devops.aishu.cn' "$TEST_DIR/test_file.txt" || echo "  没有找到匹配项"
echo ""

echo "2. 使用 grep -n -i 'devops.aishu.cn' (不区分大小写):"
grep -n -i 'devops.aishu.cn' "$TEST_DIR/test_file.txt" || echo "  没有找到匹配项"
echo ""

echo "3. 使用 grep -n 'DEVOPS.AISHU.CN' (区分大小写):"
grep -n 'DEVOPS.AISHU.CN' "$TEST_DIR/test_file.txt" || echo "  没有找到匹配项"
echo ""

echo "4. 使用 grep -n 'greed' (区分大小写):"
grep -n 'greed' "$TEST_DIR/test_file.txt" || echo "  没有找到匹配项"
echo ""

echo "5. 使用 grep -n 'GREED' (区分大小写):"
grep -n 'GREED' "$TEST_DIR/test_file.txt" || echo "  没有找到匹配项"
echo ""

echo "6. 使用 grep -n 'AISHUDevOps' (区分大小写):"
grep -n 'AISHUDevOps' "$TEST_DIR/test_file.txt" || echo "  没有找到匹配项"
echo ""

echo "7. 使用 grep -n 'aishudevops' (区分大小写):"
grep -n 'aishudevops' "$TEST_DIR/test_file.txt" || echo "  没有找到匹配项"
echo ""

# 清理测试文件
rm -rf "$TEST_DIR"

echo "=== 测试完成 ==="
echo ""
echo "结论："
echo "- 我们的脚本使用 'grep -n' (不带 -i 参数)"
echo "- 这意味着搜索是严格区分大小写的"
echo "- 只有完全匹配大小写的关键字才会被找到"
echo ""
echo "示例："
echo "- 搜索 'devops.aishu.cn' 只会匹配 'devops.aishu.cn'"
echo "- 不会匹配 'DEVOPS.AISHU.CN' 或 'DevOps.AiShu.Cn'"
