#version=DEVEL
# Use graphical install
graphical


%packages
@^minimal-environment

%end

# Keyboard layouts
keyboard --xlayouts='us'
# System language
lang en_US.UTF-8

# Network information
network  --hostname=localhost.localdomain

# Use CDROM installation media
cdrom

# Run the Setup Agent on first boot
firstboot --enable
# System services
services --enabled="chronyd"

# System timezone
timezone Asia/Shanghai --utc

# Root password
rootpw --iscrypted $6$WR6vUTzBDayfN.2Y$Y.aBVxPls402ZgnVsy6XgCEzKPj38I4fXmYwAivaB.mGBbKxXBsFNMPs5MY6We0bR6kL6vjP0BZTEMf0AZMU31
# User euler
user --name=euler --password=$6$cZLPsh8w8AHu93./$Ys3wlArO.VFENJeJR3esXnMWUtPNjkqxAGgwEKy7WdsFXhHGIXHuviIvzz/R/LayYkafKNDqcImsFOgAkTVTw/ --iscrypted --gecos="euler"

# Reboot System
reboot

%addon com_redhat_kdump --disable --reserve-mb='128'

%end

%anaconda
pwpolicy root --minlen=8 --minquality=1 --strict --nochanges --notempty
pwpolicy user --minlen=8 --minquality=1 --strict --nochanges --emptyok
pwpolicy luks --minlen=8 --minquality=1 --strict --nochanges --notempty
%end

