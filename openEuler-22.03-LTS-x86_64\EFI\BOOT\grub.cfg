set default="1"

function load_video {
  insmod efi_gop
  insmod efi_uga
  insmod video_bochs
  insmod video_cirrus
  insmod all_video
}

load_video
set gfxpayload=keep
insmod gzio
insmod part_gpt
insmod ext2

set timeout=60
### END /etc/grub.d/00_header ###

search --no-floppy --set=root -l 'openEuler-22.03-LTS-x86_64'

### BEGIN /etc/grub.d/10_linux ###
menuentry 'AutoInstall openEuler 22.03-LTS' --class openEuler --class gnu-linux --class gnu --class os {
	linuxefi /images/pxeboot/vmlinuz inst.ks=cdrom:/efilinux/efi.conf/KSMENNU fpi_to_tail=off 
	initrdefi /images/pxeboot/initrd.img
}
menuentry 'Test this media & AutoInstall openEuler 22.03-LTS' --class openEuler --class gnu-linux --class gnu --class os {
	linuxefi /images/pxeboot/vmlinuz inst.ks=cdrom:/efilinux/efi.conf/KSMENNU rd.live.check fpi_to_tail=off 
	initrdefi /images/pxeboot/initrd.img
}
menuentry 'Install openEuler 22.03-LTS' --class openEuler --class gnu-linux --class gnu --class os {
	linuxefi /images/pxeboot/vmlinuz inst.ks=cdrom:/efilinux/efi.conf/MANUALKS fpi_to_tail=off 
	initrdefi /images/pxeboot/initrd.img
}
menuentry 'Test this media & Install openEuler 22.03-LTS' --class openEuler --class gnu-linux --class gnu --class os {
	linuxefi /images/pxeboot/vmlinuz inst.ks=cdrom:/efilinux/efi.conf/MANUALKS rd.live.check fpi_to_tail=off 
	initrdefi /images/pxeboot/initrd.img
}
#menuentry 'Install openEuler 22.03-LTS' --class openEuler --class gnu-linux --class gnu --class os {
#	linuxefi /images/pxeboot/vmlinuz inst.stage2=hd:LABEL=openEuler-22.03-LTS-x86_64 fpi_to_tail=off
#	initrdefi /images/pxeboot/initrd.img
#}
#menuentry 'Test this media & install openEuler 22.03-LTS' --class openEuler --class gnu-linux --class gnu --class os {
#	linuxefi /images/pxeboot/vmlinuz inst.stage2=hd:LABEL=openEuler-22.03-LTS-x86_64 rd.live.check fpi_to_tail=off
#	initrdefi /images/pxeboot/initrd.img
#}
#submenu 'Troubleshooting -->' {
#	menuentry 'Install openEuler 22.03-LTS in basic graphics mode' --class openEuler --class gnu-linux --class gnu --class os {
#		linuxefi /images/pxeboot/vmlinuz inst.stage2=hd:LABEL=openEuler-22.03-LTS-x86_64 nomodeset fpi_to_tail=off
#		initrdefi /images/pxeboot/initrd.img
#	}
#	menuentry 'Rescue a openEuler system' --class openEuler --class gnu-linux --class gnu --class os {
#		linuxefi /images/pxeboot/vmlinuz inst.stage2=hd:LABEL=openEuler-22.03-LTS-x86_64 rescue fpi_to_tail=off
#		initrdefi /images/pxeboot/initrd.img
#	}
#}
