# Git仓库关键字扫描工具

这是一套用于检测Git仓库中敏感关键字的DevOps脚本工具。

## 脚本说明

### 1. scan_repos.sh - 完整功能版本
功能最全面的扫描脚本，支持自定义关键字、并行处理、详细报告等。

**特性:**
- 支持自定义关键字
- 并行扫描多个仓库
- 生成详细的统计报告
- 支持大文件过滤
- 完整的日志记录

**用法:**
```bash
# 基本用法
./scan_repos.sh -d /path/to/linux -k "devops.aishu.cn,greed,flora"

# 完整参数
./scan_repos.sh -d /path/to/repositories \
                -k "devops.aishu.cn,AISHUDevOps,greed" \
                -o results.txt \
                -j 8
```

### 2. quick_scan.sh - 快速扫描版本
针对特定关键字的快速扫描脚本，预设了常见的敏感关键字。

**特性:**
- 预设关键字（基于您提供的图片内容）
- 简单易用，一键扫描
- 快速生成结果

**用法:**
```bash
# 扫描当前目录下的linux文件夹
./quick_scan.sh

# 扫描指定目录
./quick_scan.sh /path/to/your/repositories
```

## 预设关键字

基于您图片中的内容，脚本预设了以下关键字：
- `devops.aishu.cn`
- `AISHUDevOps`
- `flora/_git/greed`
- `greed`
- `aishu.cn`

## 输出格式

扫描结果以管道分隔的格式输出：
```
仓库路径 | 分支/标签 | 文件路径 | 行号 | 关键字 | 行内容
```

示例：
```
/path/to/repo1 | branch:main | src/config.py | 15 | devops.aishu.cn | url = "https://devops.aishu.cn/api"
/path/to/repo2 | tag:v1.0 | README.md | 23 | greed | See greed project for details
```

## 使用步骤

### 1. 准备环境
```bash
# 给脚本执行权限
chmod +x scan_repos.sh
chmod +x quick_scan.sh

# 确保系统有必要的工具
which git grep find xargs
```

### 2. 快速开始
```bash
# 如果您的代码仓库在 ./linux 目录下
./quick_scan.sh

# 如果在其他目录
./quick_scan.sh /path/to/your/linux/repositories
```

### 3. 高级用法
```bash
# 自定义关键字扫描
./scan_repos.sh -d /path/to/repositories \
                -k "sensitive_keyword1,sensitive_keyword2" \
                -o my_results.txt \
                -j 4
```

## 输出文件

### 结果文件
- 主结果文件：`scan_results_YYYYMMDD_HHMMSS.txt`
- 包含所有匹配项的详细信息

### 报告文件
- 统计报告：`scan_results_YYYYMMDD_HHMMSS_report.txt`
- 包含按仓库、关键字、文件类型的统计信息

### 日志文件
- 扫描日志：`scan_YYYYMMDD_HHMMSS.log`
- 记录扫描过程中的所有操作和错误

## 性能优化

### 文件过滤
- 自动跳过大于10MB的文件
- 只扫描文本文件，跳过二进制文件
- 忽略.git目录内容

### 并行处理
- 支持多进程并行扫描
- 默认4个并行作业，可通过-j参数调整

## 注意事项

1. **权限要求**: 确保对目标目录有读取权限
2. **Git仓库**: 只扫描包含.git目录的仓库
3. **分支切换**: 脚本会临时切换分支，扫描完成后恢复原分支
4. **大文件**: 自动跳过大文件以提高性能
5. **网络仓库**: 如果仓库有远程分支，确保网络连接正常

## 故障排除

### 常见问题

1. **权限错误**
```bash
# 解决方案：检查目录权限
ls -la /path/to/repositories
```

2. **Git命令失败**
```bash
# 解决方案：检查Git配置
git config --global user.name "Scanner"
git config --global user.email "<EMAIL>"
```

3. **脚本执行权限**
```bash
# 解决方案：添加执行权限
chmod +x *.sh
```

## 扩展功能

### 添加新关键字
编辑脚本中的KEYWORDS数组：
```bash
KEYWORDS=(
    "devops.aishu.cn"
    "your_new_keyword"
    "another_keyword"
)
```

### 自定义文件过滤
修改`is_text_file`函数来调整文件过滤规则。

### 输出格式定制
修改输出部分的printf语句来改变输出格式。

## 示例场景

### 场景1：安全审计
检查代码中是否包含敏感的内部域名或服务地址：
```bash
./scan_repos.sh -d /company/repositories \
                -k "internal.company.com,dev.company.com,staging.company.com"
```

### 场景2：依赖检查
查找特定项目或库的引用：
```bash
./scan_repos.sh -d /projects \
                -k "deprecated_library,old_api_endpoint"
```

### 场景3：合规检查
检查是否包含需要清理的特定内容：
```bash
./quick_scan.sh /path/to/open_source_projects
```

## 技术支持

如果遇到问题，请检查：
1. 脚本执行权限
2. 目标目录是否存在且可访问
3. Git仓库是否完整
4. 系统是否有足够的磁盘空间存储结果

建议在小范围测试后再进行大规模扫描。
