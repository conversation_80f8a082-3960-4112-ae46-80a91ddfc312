#!/bin/bash
# 快速扫描脚本 - 检测代码仓库中的特定关键字
# 专门用于检测 devops.aishu.cn/AISHUDevOps/flora/_git/greed 等关键字

# 默认配置
SCAN_DIR="${1:-./linux}"  # 默认扫描当前目录下的linux文件夹
OUTPUT_FILE="scan_results_$(date +%Y%m%d_%H%M%S).txt"

# 要搜索的关键字（根据图片内容）
KEYWORDS=(
    "devops.aishu.cn"
    "AISHUDevOps"
    "flora/_git/greed"
    "greed"
    "aishu.cn"
)

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

echo -e "${GREEN}=== Git仓库关键字扫描工具 ===${NC}"
echo "扫描目录: $SCAN_DIR"
echo "输出文件: $OUTPUT_FILE"
echo "关键字: ${KEYWORDS[*]}"
echo ""

# 检查目录是否存在
if [[ ! -d "$SCAN_DIR" ]]; then
    echo -e "${RED}错误: 目录 $SCAN_DIR 不存在${NC}"
    echo "用法: $0 [扫描目录]"
    echo "示例: $0 /path/to/linux"
    exit 1
fi

# 创建输出文件
{
    echo "# Git仓库关键字扫描结果"
    echo "# 扫描时间: $(date)"
    echo "# 扫描目录: $SCAN_DIR"
    echo "# 关键字: ${KEYWORDS[*]}"
    echo "# 格式: 仓库路径 | 分支/标签 | 文件路径 | 行号 | 关键字 | 行内容"
    echo "#"
} > "$OUTPUT_FILE"

# 统计变量
total_repos=0
scanned_repos=0
total_matches=0

# 查找并扫描Git仓库
echo -e "${BLUE}正在查找Git仓库...${NC}"

find "$SCAN_DIR" -type d -name ".git" | while read -r git_dir; do
    repo_dir=$(dirname "$git_dir")
    repo_name=$(basename "$repo_dir")

    echo -e "${YELLOW}扫描仓库: $repo_name${NC}"

    cd "$repo_dir" || continue

    # 保存当前分支
    current_branch=$(git rev-parse --abbrev-ref HEAD 2>/dev/null || echo "")

    # 获取所有分支和标签
    refs=()

    # 添加分支
    while IFS= read -r branch; do
        branch=$(echo "$branch" | sed 's/^[* ] *//' | sed 's/remotes\/origin\///')
        if [[ "$branch" != "HEAD" && "$branch" != "" ]]; then
            refs+=("branch:$branch")
        fi
    done < <(git branch -a 2>/dev/null)

    # 添加标签
    while IFS= read -r tag; do
        if [[ -n "$tag" ]]; then
            refs+=("tag:$tag")
        fi
    done < <(git tag 2>/dev/null)

    # 扫描每个分支和标签
    for ref in "${refs[@]}"; do
        ref_type=$(echo "$ref" | cut -d: -f1)
        ref_name=$(echo "$ref" | cut -d: -f2-)

        echo "  检查 $ref_type: $ref_name"

        # 切换到指定分支/标签
        if git checkout "$ref_name" >/dev/null 2>&1; then

            # 获取所有文件并搜索关键字
            git ls-files | while read -r file; do
                if [[ -f "$file" ]]; then
                    # 检查文件大小（跳过大文件）
                    size=$(stat -c%s "$file" 2>/dev/null || stat -f%z "$file" 2>/dev/null || echo "0")
                    if [[ "$size" -gt 10485760 ]]; then  # 10MB
                        continue
                    fi

                    # 检查是否为文本文件
                    if file "$file" 2>/dev/null | grep -q -E "(text|empty|ASCII|UTF-8)"; then

                        # 搜索每个关键字（区分大小写）
                        for keyword in "${KEYWORDS[@]}"; do
                            matches=$(grep -n "$keyword" "$file" 2>/dev/null || true)
                            if [[ -n "$matches" ]]; then
                                echo "$matches" | while IFS=: read -r line_num line_content; do
                                    echo "$repo_dir | $ref_type:$ref_name | $file | $line_num | $keyword | ${line_content:0:200}" >> "$OUTPUT_FILE"
                                    echo -e "    ${GREEN}找到匹配:${NC} $file:$line_num - $keyword"
                                done
                            fi
                        done
                    fi
                fi
            done
        fi
    done

    # 恢复原分支
    if [[ -n "$current_branch" && "$current_branch" != "HEAD" ]]; then
        git checkout "$current_branch" >/dev/null 2>&1 || true
    fi

done

# 生成统计信息
echo ""
echo -e "${GREEN}=== 扫描完成 ===${NC}"

# 统计结果
if [[ -f "$OUTPUT_FILE" ]]; then
    match_count=$(grep -v "^#" "$OUTPUT_FILE" | wc -l)

    if [[ "$match_count" -gt 0 ]]; then
        echo "总匹配数: $match_count"
        echo "结果已保存到: $OUTPUT_FILE"

        echo ""
        echo -e "${BLUE}=== 统计摘要 ===${NC}"

        # 按仓库统计
        echo "按仓库统计:"
        grep -v "^#" "$OUTPUT_FILE" | cut -d'|' -f1 | sort | uniq -c | sort -nr | head -10

        echo ""
        echo "按关键字统计:"
        grep -v "^#" "$OUTPUT_FILE" | cut -d'|' -f5 | sort | uniq -c | sort -nr

        echo ""
        echo "详细结果示例 (前5条):"
        echo "----------------------------------------"
        grep -v "^#" "$OUTPUT_FILE" | head -5 | while IFS='|' read -r repo ref file line_num keyword line_content; do
            echo "仓库: $(basename "$repo")"
            echo "  分支/标签: $ref"
            echo "  文件: $file:$line_num"
            echo "  关键字: $keyword"
            echo "  内容: ${line_content:0:100}..."
            echo ""
        done

    else
        echo "未找到任何匹配项"
    fi
else
    echo "扫描过程中出现错误"
fi

echo ""
echo "扫描日志和详细结果请查看: $OUTPUT_FILE"
